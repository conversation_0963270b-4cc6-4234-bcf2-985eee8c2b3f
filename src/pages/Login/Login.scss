@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

// bulma overrides
$card-content-padding: 4rem;
$input-shadow: unset;
$input-border-color: $grey-dark;
$input-color: $grey-darker;
$input-placeholder-color: $grey-darker;

@import "bulma/sass/utilities/all";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/button";
@import "bulma/sass/components/card";
@import "bulma/sass/form/all";

.login-container *{
  font-family: $primary-font;
}

.login-wrapper{
  align-content: center;
  height: 100%;
  min-height: 100vh;
}

.divider{
  margin-left: 10px;
  margin-right: 10px;
}


.login-container {

  @media (max-width: 1600px) {
    zoom: 0.75;
  }
  
  .heading-text {
    font-weight: 700;
    font-size: 3.37rem;
    color: $white;
    text-align: center;
    line-height: 1.2em;
    letter-spacing: -1px;

    @media (max-width:550px) {
        font-size: 2.8rem;
      }
  }


  .subtitle-text {
    font-size: 1.5rem;
    color: $white;
    text-align: center;
  }

  .login-card {
    width: 100%;
    border-radius: 16px;
    box-shadow: 0 8px 17px #00000029;

    .card-content {
      padding: 4rem;
      padding-bottom: 2rem;

      @include until($tablet) {
        padding: 2rem;
      }

      .content{

        .field input{
          max-width: 450px;
        }

        .terms-condition-text {
          font-size: 13px;
        }

        .login-btn{
          width: 100%;
          max-width: 297px;
          color: #000 !important;
          background: #fac44b !important;
          border-radius: 15px;
          border: none !important;
          padding: 2rem !important;

          &:hover{
            background: #f3c254 !important;
          }

        }
        
      }

      .reviews-container{
        gap: 24px;

        @media (max-width:550px) {
          flex-direction: column;
        }

        .review-box{
          justify-items: center;
          text-align: center;
        }


      }

      .google-separator{

        .google-button{
          width: 100%;
          max-width: 380px;
          justify-self: center;
          padding: 1.65rem 1rem !important;
          justify-content: space-between;
          transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.3s ease;

          &:hover{
            background-color: #f9f9f9;
            border-color: #dbdbdb !important;
          }
        }

        .separator {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .separator .line {
          width: 100%;
          max-width: 187px;
          height: 1px;
          background: #dbdbdb;
        }

        .separator .or {
          margin: 0 0.5rem;
          font-size: 1.2rem;
          color: #7a7a7a;
        }
      }
      
      .underline-hover{
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }


  @media screen and (max-height: 900px) {
    .login-container {
      align-items: center;
      bottom: 0;
      display: flex;
      flex-direction: column;
      gap: .5rem;
      height: 33.75rem;
      /* position: fixed; */
      /* right: 32.5%; */
      /* top: 18%; */
      width: 80%!important;
      margin-left: auto!important;
      margin-right: auto!important;
  }
  }

}

.eye-icon{
  position: absolute!important;
  top: 10px!important;
  bottom: 0!important;
  margin: auto -30px!important;
}

.error-msg {
  font-size: 1rem;
}

